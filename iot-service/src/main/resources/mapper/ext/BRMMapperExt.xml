<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.BRMMapperExt">
  <select id="getBRMUploadList"  resultType="com.chinamobile.iot.sc.pojo.dto.BRMUploadDTO">
      select atom.id,
             sku.id as skuId,
             atom.inventory_main_id as inId,
             sku.template_id            as cardTempleCode,
             sku.template_name          as cardTempleName,
             sku.cust_code              as cardVenderCode,
             sku.cust_name              as cardVenderName,
             if(didi.location is null, didi.be_id, didi.location) as region,
             boi.goods_id               as bossId,
             dimi.device_version        as xModelName,
             boi.iot_mall_offering_name as vasName,
             didi.reserve_quatity       as xPickNum,
      if(didi.current_inventory &lt; 0,0,didi.current_inventory)     as xStockNum,
      if(cimi.current_inventory &lt; 0,0,cimi.current_inventory)      as cardStockNum,
             cimi.reserve_quatity       as cardPickNum,
             if(s.saleNum is null,0,s.saleNum)                  as saleNum
      from
          sku_offering_info sku
              left join benefit_offering bo on sku.offering_code = bo.sku_offering_code
              left join benefit_offerings_info boi on bo.benefit_offerings_info_id = boi.id

              left join atom_offering_info atom on atom.sku_code = bo.sku_offering_code
              left join dkcardx_inventory_main_info dimi on dimi.id = atom.inventory_main_id
              inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
              left join card_inventory_main_info cimi
                        on sku.template_id = cimi.template_id and sku.cust_code = cimi.cust_code
              LEFT JOIN (SELECT subquery.sku_offering_code,
                                COUNT(subquery.sku_quantity) AS saleNum
                         FROM (
                                  SELECT DISTINCT o2cai.order_id, o2cai.sku_offering_code,o2cai.sku_quantity
                                  FROM order_2c_atom_info o2cai
                                  WHERE o2cai.order_status = 7

                              ) AS subquery
                         GROUP BY subquery.sku_offering_code) s ON s.sku_offering_code = sku.offering_code
      where 1 = 1
        and boi.goods_id is not null
        and sku.offering_status = '1'
        and sku.delete_time is null
        and boi.iot_release_area_id in
            ('200', '7530', '7600', '7620', '7520', '6600', '7540', '7510', '7500', '6680', '7680', '2000', '7630', '7660',
             '6620', '7560', '7590', '7570', '7690', '7550', '7580', '6630')
        and atom.card_containing_terminal = '1'
        and sku.template_id is not null
  </select>

  <select id="getBRMDetailUploadList" resultType="com.chinamobile.iot.sc.pojo.dto.BRMDetailUploadDTO">
      select
          cr.device_version as deviceModel,
          cr.template_id as cardTemplateCode,
          '中国移动' as brand,
          'IOT-BRM-ECSS' as dataSource,
          if(cr.location is null, cr.be_id, cr.location) as regionCode,
          if(didi.reserve_quatity is null, 0, didi.reserve_quatity) as reserveQuantity,
          if(didi.current_inventory &lt; 0, 0, didi.current_inventory) as currentInventory,
          if(didi.total_inventory is null, 0, didi.total_inventory) as totalInventory,
          -- 可用设备明细（IMEI列表，用&,隔开）
          GROUP_CONCAT(DISTINCT CASE WHEN cr.sell_status = '1' THEN cr.imei END SEPARATOR '&,') as availableDeviceDetails,
          -- 销量数（昨日销售数）
          if(yesterday_sales.yesterdaySaleNum is null, 0, yesterday_sales.yesterdaySaleNum) as salesQuantity,
          -- 销量设备明细（已销售的IMEI列表，用&,隔开）
          GROUP_CONCAT(DISTINCT CASE WHEN cr.sell_status = '3' THEN cr.imei END SEPARATOR '&,') as salesDeviceDetails,
          -- 关联规格商品（规格商品编码|预占数|昨日销量数|规格商品名称，用&,隔开，字段用&#隔开）
          GROUP_CONCAT(DISTINCT CONCAT(
              diai.offering_code, '&#',
              if(diai.atom_inventory is null, 0, diai.atom_inventory), '&#',
              if(atom_yesterday_sales.yesterdaySaleNum is null, 0, atom_yesterday_sales.yesterdaySaleNum), '&#',
              atom.offering_name
          ) SEPARATOR '&,') as relatedSkuProducts,
          -- 可用设备明细条数
          COUNT(DISTINCT CASE WHEN cr.sell_status = '1' THEN cr.imei END) as availableDeviceCount,
          -- 销量设备明细条数
          COUNT(DISTINCT CASE WHEN cr.sell_status = '3' THEN cr.imei END) as salesDeviceCount
      from
          card_relation cr
              -- 关联库存主表（以be_id+device_version+terminal_type+template_id关联）
              left join dkcardx_inventory_main_info dimi on
                  dimi.be_id = cr.be_id
                  and dimi.device_version = cr.device_version
                  and dimi.terminal_type = cr.terminal_type
                  and dimi.template_id = cr.template_id
              -- 关联库存详情表
              left join dkcardx_inventory_detail_info didi on didi.inventory_main_id = dimi.id
              -- 关联库存原子表（以inventory_main_id关联）
              left join dkcardx_inventory_atom_info diai on diai.inventory_main_id = dimi.id
              -- 根据atom_id获取原子商品信息
              left join atom_offering_info atom on atom.id = diai.atom_id
              -- 原子商品昨日销售数查询（从order_2c_atom_history表查询inner_status=64且create_time为昨天的数据）
              LEFT JOIN (
                  SELECT
                      o2cai.atom_offering_code,
                      SUM(o2cai.sku_quantity * o2cai.atom_quantity) AS yesterdaySaleNum
                  FROM order_2c_atom_history o2cah
                  INNER JOIN order_2c_atom_info o2cai ON o2cah.atom_order_id = o2cai.id
                  WHERE o2cah.inner_status = 64
                    AND DATE(o2cah.create_time) = DATE(DATE_SUB(NOW(), INTERVAL 1 DAY))
                  GROUP BY o2cai.atom_offering_code
              ) atom_yesterday_sales ON atom_yesterday_sales.atom_offering_code = atom.offering_code
              -- 设备型号维度的昨日销售数汇总
              LEFT JOIN (
                  SELECT
                      cr_sales.device_version,
                      cr_sales.template_id,
                      if(cr_sales.location is null, cr_sales.be_id, cr_sales.location) as regionCode,
                      SUM(o2cai_sales.sku_quantity * o2cai_sales.atom_quantity) AS yesterdaySaleNum
                  FROM order_2c_atom_history o2cah_sales
                  INNER JOIN order_2c_atom_info o2cai_sales ON o2cah_sales.atom_order_id = o2cai_sales.id
                  INNER JOIN card_relation cr_sales ON cr_sales.order_atom_info_id = o2cai_sales.id
                  WHERE o2cah_sales.inner_status = 64
                    AND DATE(o2cah_sales.create_time) = DATE(DATE_SUB(NOW(), INTERVAL 1 DAY))
                  GROUP BY cr_sales.device_version, cr_sales.template_id, if(cr_sales.location is null, cr_sales.be_id, cr_sales.location)
              ) yesterday_sales ON yesterday_sales.device_version = cr.device_version
                                AND yesterday_sales.template_id = cr.template_id
                                AND yesterday_sales.regionCode = if(cr.location is null, cr.be_id, cr.location)
      where 1 = 1
        and cr.delete_time is null
        and cr.device_version is not null
        and cr.template_id is not null
      GROUP BY cr.device_version, cr.template_id, if(cr.location is null, cr.be_id, cr.location),
               didi.reserve_quatity, didi.current_inventory, didi.total_inventory
  </select>
</mapper>